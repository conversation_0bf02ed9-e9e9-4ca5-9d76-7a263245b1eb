generator client {
  provider = "prisma-client-js"
  output   = "../lib/generated/prisma"
}

datasource db {
  provider = "mongodb"
  url      = env("DATABASE_URL")
}

enum UserRole {
  ADMIN
  USER
  PREMIUM_USER
}

model User {
  id       String    @id @default(cuid()) @map("_id")
  name     String?
  email    String    @unique
  image    String?
  role     UserRole  @default(USER)
  accounts Account[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Account {
  id                String  @id @default(cuid()) @map("_id")
  userId            String  @map("user_id")
  type              String
  provider          String
  providerAccountId String
  refreshToken      String? @map("refresh_token")
  accessToken       String? @map("access_token")
  expiresAt         Int?    @map("expires_at")
  tokenType         String? @map("token_type")
  scope             String?
  idToken           String? @map("id_token")
  sessionState      String? @map("session_state")

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@index([userId])
}
